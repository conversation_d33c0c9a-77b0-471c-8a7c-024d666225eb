import { useState, useEffect, useMemo, useCallback } from 'react'
import { Bar<PERSON>hart3, TrendingUp, TrendingDown, Target, AlertTriangle } from 'lucide-react'

// Type definitions
interface StatsResponse {
  totalTrades: number
  winRate: number
  averageProfit: number
  averageLoss: number
  largestWin: number
  largestLoss: number
  totalProfit: number
}

interface PerformanceMetrics {
  totalTrades: number
  winRate: number
  profitFactor: number
  maxDrawdown: number
  averageWin: number
  averageLoss: number
  largestWin: number
  largestLoss: number
  consecutiveWins: number
  consecutiveLosses: number
}

// Constants
const CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'] as const
const INITIAL_EQUITY = 1000
const EQUITY_DAYS_RANGE = 30
const EQUITY_MULTIPLIER = 5.2
const MAX_DRAWDOWN_CAP = 25
const PROBABILITY_THRESHOLD = 0.01
const WIN_RATE_THRESHOLDS = {
  EXCELLENT: 70,
  GOOD: 60
} as const

// Color constants for consistent theming
const COLORS = {
  SUCCESS: 'text-green-400',
  WARNING: 'text-yellow-400',
  ERROR: 'text-red-400',
  PRIMARY: 'text-blue-400',
  SECONDARY: 'text-purple-400',
  NEUTRAL: 'text-white',
  MUTED: 'text-gray-400'
} as const

// Strategy performance constants
const STRATEGY_PERFORMANCE = {
  RSI: { trades: 18, winRate: 77.8, profit: 124.5 },
  MACD: { trades: 15, winRate: 66.7, profit: 89.3 },
  BOLLINGER: { trades: 14, winRate: 71.4, profit: 67.8 }
} as const

const PerformancePanel: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    totalTrades: 0,
    winRate: 0,
    profitFactor: 0,
    maxDrawdown: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
    consecutiveWins: 0,
    consecutiveLosses: 0
  })

  const [selectedPeriod, setSelectedPeriod] = useState('today')
  const [equityData, setEquityData] = useState<
    Array<{ time: string; equity: number; drawdown: number }>
  >([])
  const [strategyData, setStrategyData] = useState<
    Array<{ name: string; trades: number; winRate: number; profit: number }>
  >([])
  const [assetData, setAssetData] = useState<Array<{ name: string; value: number; color: string }>>(
    []
  )

  // Safe calculation utilities
  const calculateProfitFactor = useCallback(
    (averageProfit: number, averageLoss: number): number => {
      const absLoss = Math.abs(averageLoss)
      if (absLoss === 0) {
        return averageProfit > 0 ? 999 : 1
      }
      return averageProfit / absLoss
    },
    []
  )

  const calculateRiskRewardRatio = useCallback(
    (averageWin: number, averageLoss: number): string => {
      const absLoss = Math.abs(averageLoss)
      if (absLoss === 0) {
        return '∞'
      }
      return (averageWin / absLoss).toFixed(2)
    },
    []
  )

  const validateStatsResponse = (stats: unknown): StatsResponse => {
    if (!stats || typeof stats !== 'object') {
      throw new Error('Invalid stats response: not an object')
    }

    const requiredFields: (keyof StatsResponse)[] = [
      'totalTrades',
      'winRate',
      'averageProfit',
      'averageLoss',
      'largestWin',
      'largestLoss',
      'totalProfit'
    ]

    const statsObj = stats as Record<string, unknown>
    for (const field of requiredFields) {
      if (typeof statsObj[field] !== 'number') {
        throw new Error(`Invalid stats response: ${field} is not a number`)
      }
    }

    return statsObj as unknown as StatsResponse
  }

  // Calculate estimated advanced metrics from available stats
  const calculateAdvancedMetrics = useCallback((stats: StatsResponse) => {
    // Estimate max drawdown based on win rate and average loss
    // Lower win rates and higher average losses suggest higher drawdowns
    const winRateDecimal = stats.winRate / 100
    const lossRateDecimal = 1 - winRateDecimal

    // Estimate based on risk of ruin formula and consecutive losses
    const estimatedMaxDrawdown = Math.min(
      (Math.abs(stats.averageLoss) * 3 * lossRateDecimal * 100) / INITIAL_EQUITY,
      MAX_DRAWDOWN_CAP
    )

    // Estimate consecutive wins/losses based on win rate
    // Higher win rates suggest longer win streaks, lower win rates suggest longer loss streaks
    const estimatedConsecutiveWins = Math.max(
      Math.round(Math.log(PROBABILITY_THRESHOLD) / Math.log(1 - winRateDecimal)),
      Math.round(stats.totalTrades * winRateDecimal * 0.3)
    )

    const estimatedConsecutiveLosses = Math.max(
      Math.round(
        Math.log(PROBABILITY_THRESHOLD) / Math.log(winRateDecimal || PROBABILITY_THRESHOLD)
      ),
      Math.round(stats.totalTrades * lossRateDecimal * 0.2)
    )

    return {
      maxDrawdown: Math.round(estimatedMaxDrawdown * 100) / 100,
      consecutiveWins: Math.min(estimatedConsecutiveWins, Math.round(stats.totalTrades * 0.4)),
      consecutiveLosses: Math.min(estimatedConsecutiveLosses, Math.round(stats.totalTrades * 0.3))
    }
  }, [])

  const generateEquityData = useCallback(() => {
    const data: Array<{ time: string; equity: number; drawdown: number }> = []
    let equity = INITIAL_EQUITY
    let peak = INITIAL_EQUITY
    const now = new Date()

    for (let i = EQUITY_DAYS_RANGE; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      const change = (Math.random() - 0.4) * 50 // Slight upward bias
      equity += change

      // Update peak efficiently - O(1) operation
      peak = Math.max(peak, equity)

      // Calculate drawdown correctly: (peak - current) / peak * 100
      const drawdown = peak > 0 ? ((peak - equity) / peak) * 100 : 0

      data.push({
        time: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        equity: Math.round(equity * 100) / 100,
        drawdown: Math.round(Math.max(0, drawdown) * 100) / 100
      })
    }

    setEquityData(data)
  }, [])

  const loadPerformanceData = useCallback(async () => {
    try {
      // Load real sample data with validation
      const rawStats = await window.api.sampleData.getStats()
      const stats = validateStatsResponse(rawStats)

      // Calculate advanced metrics from stats
      const advancedMetrics = calculateAdvancedMetrics(stats)

      setMetrics({
        totalTrades: stats.totalTrades,
        winRate: stats.winRate,
        profitFactor: calculateProfitFactor(stats.averageProfit, stats.averageLoss),
        maxDrawdown: advancedMetrics.maxDrawdown,
        averageWin: stats.averageProfit,
        averageLoss: stats.averageLoss,
        largestWin: stats.largestWin,
        largestLoss: stats.largestLoss,
        consecutiveWins: advancedMetrics.consecutiveWins,
        consecutiveLosses: advancedMetrics.consecutiveLosses
      })

      // Generate equity curve data
      generateEquityData()

      // Generate strategy performance data
      setStrategyData([
        { name: 'RSI Strategy', trades: 18, winRate: 77.8, profit: 124.5 },
        { name: 'MACD Strategy', trades: 15, winRate: 66.7, profit: 89.3 },
        { name: 'Bollinger Bands', trades: 14, winRate: 71.4, profit: 67.8 },
        {
          name: 'Combined',
          trades: stats.totalTrades - 47,
          winRate: stats.winRate,
          profit: stats.totalProfit - 281.6
        }
      ])

      // Generate asset distribution data
      setAssetData([
        { name: 'EUR/USD', value: 35, color: CHART_COLORS[0] },
        { name: 'BTC/USD', value: 25, color: CHART_COLORS[1] },
        { name: 'GBP/USD', value: 20, color: CHART_COLORS[2] },
        { name: 'ETH/USD', value: 12, color: CHART_COLORS[3] },
        { name: 'Others', value: 8, color: CHART_COLORS[4] }
      ])
    } catch (error) {
      console.error('Failed to load performance data:', error)
      // Fallback to mock data with calculated metrics
      const fallbackStats: StatsResponse = {
        totalTrades: 47,
        winRate: 72.5,
        averageProfit: 15.2,
        averageLoss: -12.8,
        largestWin: 45.6,
        largestLoss: -28.9,
        totalProfit: 281.6
      }

      const advancedMetrics = calculateAdvancedMetrics(fallbackStats)

      setMetrics({
        totalTrades: fallbackStats.totalTrades,
        winRate: fallbackStats.winRate,
        profitFactor: calculateProfitFactor(fallbackStats.averageProfit, fallbackStats.averageLoss),
        maxDrawdown: advancedMetrics.maxDrawdown,
        averageWin: fallbackStats.averageProfit,
        averageLoss: fallbackStats.averageLoss,
        largestWin: fallbackStats.largestWin,
        largestLoss: fallbackStats.largestLoss,
        consecutiveWins: advancedMetrics.consecutiveWins,
        consecutiveLosses: advancedMetrics.consecutiveLosses
      })
      generateEquityData()
    }
  }, [calculateProfitFactor, calculateAdvancedMetrics, generateEquityData])

  // This will be defined after the functions below
  useEffect(() => {
    let isMounted = true

    const loadData = async (): Promise<void> => {
      try {
        await loadPerformanceData()
      } catch (error) {
        if (isMounted) {
          console.error('Failed to load performance data in effect:', error)
        }
      }
    }

    loadData()

    return () => {
      isMounted = false
    }
  }, [loadPerformanceData, selectedPeriod]) // We'll use selectedPeriod directly for now

  const getMetricColor = (value: number, isPositive: boolean): string => {
    if (isPositive) {
      return value > 0 ? COLORS.SUCCESS : COLORS.ERROR
    } else {
      return value < 0 ? COLORS.ERROR : COLORS.SUCCESS
    }
  }

  const getWinRateColor = (winRate: number): string => {
    if (winRate >= WIN_RATE_THRESHOLDS.EXCELLENT) return COLORS.SUCCESS
    if (winRate >= WIN_RATE_THRESHOLDS.GOOD) return COLORS.WARNING
    return COLORS.ERROR
  }

  // Memoized calculations for performance
  const memoizedMetrics = useMemo(() => {
    return {
      riskRewardRatio: calculateRiskRewardRatio(metrics.averageWin, metrics.averageLoss),
      expectancy:
        (metrics.winRate / 100) * metrics.averageWin +
        (1 - metrics.winRate / 100) * metrics.averageLoss,
      currentEquity: INITIAL_EQUITY + metrics.totalTrades * EQUITY_MULTIPLIER,
      profitFactorColor: getMetricColor(metrics.profitFactor - 1, true),
      winRateColor: getWinRateColor(metrics.winRate)
    }
  }, [
    metrics.averageWin,
    metrics.averageLoss,
    metrics.winRate,
    metrics.totalTrades,
    metrics.profitFactor,
    calculateRiskRewardRatio
  ])

  // Memoized strategy data to prevent unnecessary re-renders
  const memoizedStrategyData = useMemo(
    () => [
      {
        name: 'RSI Strategy',
        trades: STRATEGY_PERFORMANCE.RSI.trades,
        winRate: STRATEGY_PERFORMANCE.RSI.winRate,
        profit: STRATEGY_PERFORMANCE.RSI.profit,
        avgTrade: STRATEGY_PERFORMANCE.RSI.profit / STRATEGY_PERFORMANCE.RSI.trades
      },
      {
        name: 'MACD Strategy',
        trades: STRATEGY_PERFORMANCE.MACD.trades,
        winRate: STRATEGY_PERFORMANCE.MACD.winRate,
        profit: STRATEGY_PERFORMANCE.MACD.profit,
        avgTrade: STRATEGY_PERFORMANCE.MACD.profit / STRATEGY_PERFORMANCE.MACD.trades
      },
      {
        name: 'Bollinger Bands',
        trades: STRATEGY_PERFORMANCE.BOLLINGER.trades,
        winRate: STRATEGY_PERFORMANCE.BOLLINGER.winRate,
        profit: STRATEGY_PERFORMANCE.BOLLINGER.profit,
        avgTrade: STRATEGY_PERFORMANCE.BOLLINGER.profit / STRATEGY_PERFORMANCE.BOLLINGER.trades
      }
    ],
    []
  )

  // Memoized periods to prevent recreation on every render
  const memoizedPeriods = useMemo(
    () => [
      { value: 'today', label: 'Today' },
      { value: 'week', label: 'This Week' },
      { value: 'month', label: 'This Month' },
      { value: 'all', label: 'All Time' }
    ],
    []
  )

  return (
    <div className="h-full bg-gray-900 p-6 overflow-y-auto">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <BarChart3 className="w-8 h-8 mr-3 text-blue-400" />
            Performance Analytics
          </h2>

          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="select-field"
          >
            {memoizedPeriods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="card">
            <div className="flex items-center space-x-3">
              <Target className="w-8 h-8 text-blue-400" />
              <div>
                <div className="text-sm text-gray-400">Total Trades</div>
                <div className="text-2xl font-bold text-white">{metrics.totalTrades}</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-8 h-8 text-green-400" />
              <div>
                <div className="text-sm text-gray-400">Win Rate</div>
                <div className={`text-2xl font-bold ${memoizedMetrics.winRateColor}`}>
                  {metrics.winRate.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <BarChart3 className="w-8 h-8 text-purple-400" />
              <div>
                <div className="text-sm text-gray-400">Profit Factor</div>
                <div className={`text-2xl font-bold ${memoizedMetrics.profitFactorColor}`}>
                  {metrics.profitFactor.toFixed(2)}
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-8 h-8 text-red-400" />
              <div>
                <div className="text-sm text-gray-400">Max Drawdown</div>
                <div className="text-2xl font-bold text-red-400">
                  {metrics.maxDrawdown.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Win/Loss Analysis */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-white">Win/Loss Analysis</h3>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Average Win</span>
                <span className="text-green-400 font-semibold">
                  +${metrics.averageWin.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Average Loss</span>
                <span className="text-red-400 font-semibold">
                  ${metrics.averageLoss.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Largest Win</span>
                <span className="text-green-400 font-semibold">
                  +${metrics.largestWin.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Largest Loss</span>
                <span className="text-red-400 font-semibold">
                  ${metrics.largestLoss.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Streak Analysis */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-white">Streak Analysis</h3>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Consecutive Wins</span>
                <span className="text-green-400 font-semibold">{metrics.consecutiveWins}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Consecutive Losses</span>
                <span className="text-red-400 font-semibold">{metrics.consecutiveLosses}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Risk/Reward Ratio</span>
                <span className="text-white font-semibold">
                  1:{memoizedMetrics.riskRewardRatio}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-400">Expectancy</span>
                <span
                  className={`font-semibold ${
                    memoizedMetrics.expectancy > 0 ? 'text-green-400' : 'text-red-400'
                  }`}
                >
                  ${memoizedMetrics.expectancy.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Equity Curve Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-white">Equity Curve & Drawdown</h3>
          </div>

          <div className="h-64 bg-gray-700 rounded-lg flex items-center justify-center border border-gray-600">
            <div className="text-center text-gray-400">
              <TrendingUp className="w-12 h-12 mx-auto mb-3 text-green-400 opacity-50" />
              <p className="text-lg font-semibold mb-2">Equity Performance Chart</p>
              <p className="text-sm">Real-time equity curve and drawdown visualization</p>
              <div className="mt-4 flex items-center justify-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Equity: ${memoizedMetrics.currentEquity.toFixed(2)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <span>Drawdown: {metrics.maxDrawdown.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Strategy Performance Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-white">Strategy Performance</h3>
            </div>

            <div className="h-64 bg-gray-700 rounded-lg flex items-center justify-center border border-gray-600">
              <div className="text-center text-gray-400">
                <BarChart3 className="w-12 h-12 mx-auto mb-3 text-blue-400 opacity-50" />
                <p className="text-lg font-semibold mb-2">Strategy Comparison</p>
                <p className="text-sm mb-4">Performance breakdown by trading strategy</p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>RSI Strategy:</span>
                    <span className="text-green-400">+$124.50</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>MACD Strategy:</span>
                    <span className="text-green-400">+$89.30</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Bollinger Bands:</span>
                    <span className="text-green-400">+$67.80</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Asset Distribution Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-white">Asset Distribution</h3>
            </div>

            <div className="h-64 bg-gray-700 rounded-lg flex items-center justify-center border border-gray-600">
              <div className="text-center text-gray-400">
                <Target className="w-12 h-12 mx-auto mb-3 text-purple-400 opacity-50" />
                <p className="text-lg font-semibold mb-2">Trading Assets</p>
                <p className="text-sm mb-4">Distribution of trades across assets</p>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                    <span>EUR/USD: 35%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span>BTC/USD: 25%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <span>GBP/USD: 20%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <span>Others: 20%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Strategy Performance */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-white">Strategy Breakdown</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Strategy</th>
                  <th className="text-right py-2 text-gray-400">Trades</th>
                  <th className="text-right py-2 text-gray-400">Win Rate</th>
                  <th className="text-right py-2 text-gray-400">Profit</th>
                  <th className="text-right py-2 text-gray-400">Avg Trade</th>
                </tr>
              </thead>
              <tbody className="text-white">
                {memoizedStrategyData.map((strategy) => (
                  <tr key={strategy.name} className="border-b border-gray-800">
                    <td className="py-2">{strategy.name}</td>
                    <td className="text-right">{strategy.trades}</td>
                    <td className={`text-right ${getWinRateColor(strategy.winRate)}`}>
                      {strategy.winRate}%
                    </td>
                    <td className="text-right text-green-400">+${strategy.profit.toFixed(2)}</td>
                    <td className="text-right text-green-400">+${strategy.avgTrade.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformancePanel
